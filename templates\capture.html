<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Cookie Capture & Analysis</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }
      .section {
        margin: 20px 0;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 5px;
        background-color: #fafafa;
      }
      .section h2 {
        margin-top: 0;
        color: #555;
      }
      .nav-links {
        text-align: center;
        margin: 20px 0;
      }
      .nav-links a {
        color: #007bff;
        text-decoration: none;
        margin: 0 15px;
        padding: 10px 15px;
        border: 1px solid #007bff;
        border-radius: 5px;
      }
      .nav-links a:hover {
        background: #007bff;
        color: white;
      }
      .input-group {
        margin: 10px 0;
      }
      input,
      textarea {
        width: 100%;
        padding: 8px;
        margin: 5px 0;
        border: 1px solid #ddd;
        border-radius: 3px;
        box-sizing: border-box;
      }
      textarea {
        height: 120px;
        resize: vertical;
      }
      button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #0056b3;
      }
      button.success {
        background: #28a745;
      }
      button.success:hover {
        background: #218838;
      }
      pre {
        background: #f8f8f8;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
        border: 1px solid #e0e0e0;
        max-height: 400px;
        overflow-y: auto;
      }
      .grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
      }
      .alert {
        padding: 15px;
        margin: 10px 0;
        border-radius: 5px;
      }
      .alert-info {
        background: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
      }
      .alert-warning {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        color: #856404;
      }
      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;
      }
      .stat-card {
        background: #e7f3ff;
        padding: 15px;
        border-radius: 5px;
        text-align: center;
      }
      .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #007bff;
      }
      .cookie-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
      }
      .cookie-table th,
      .cookie-table td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
      }
      .cookie-table th {
        background-color: #f8f9fa;
        font-weight: bold;
        color: #495057;
      }
      .cookie-table tr:nth-child(even) {
        background-color: #f8f9fa;
      }
      .cookie-table tr:hover {
        background-color: #e9ecef;
      }
      .cookie-value {
        max-width: 200px;
        word-break: break-all;
        font-family: monospace;
        font-size: 0.9em;
      }
      .security-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: bold;
        margin: 2px;
      }
      .badge-secure {
        background-color: #d4edda;
        color: #155724;
      }
      .badge-insecure {
        background-color: #f8d7da;
        color: #721c24;
      }
      .badge-httponly {
        background-color: #d1ecf1;
        color: #0c5460;
      }
      .badge-session {
        background-color: #fff3cd;
        color: #856404;
      }
      .badge-persistent {
        background-color: #e2e3e5;
        color: #383d41;
      }
      .cookie-category {
        margin: 20px 0;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
      }
      .category-header {
        background-color: #007bff;
        color: white;
        padding: 15px;
        font-weight: bold;
        font-size: 1.1em;
      }
      .category-content {
        padding: 15px;
      }
      .cookie-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .cookie-name {
        font-weight: bold;
        font-size: 1.1em;
        color: #007bff;
        margin-bottom: 8px;
      }
      .cookie-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin: 10px 0;
      }
      .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 5px 0;
        border-bottom: 1px solid #f1f3f4;
      }
      .detail-label {
        font-weight: bold;
        color: #6c757d;
      }
      .detail-value {
        color: #495057;
        font-family: monospace;
        word-break: break-all;
      }
      .tabs {
        display: flex;
        border-bottom: 2px solid #dee2e6;
        margin: 20px 0;
      }
      .tab {
        padding: 12px 24px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-bottom: none;
        cursor: pointer;
        margin-right: 5px;
        border-radius: 8px 8px 0 0;
      }
      .tab.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
      }
      .tab-content {
        display: none;
      }
      .tab-content.active {
        display: block;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>Cookie Capture & Analysis</h1>

      <div class="nav-links">
        <a href="/">Home</a>
        <a href="/demo">Demo Page</a>
        <a href="/capture">Cookie Capture</a>
        <a href="/api/session-info" target="_blank">Raw JSON Data</a>
      </div>

      <div class="alert alert-info">
        <h3>How to Capture Cookies from Websites:</h3>
        <ol>
          <li>
            <strong>Method 1:</strong> Use the URL capture to fetch cookies from
            a public page
          </li>
          <li>
            <strong>Method 2:</strong> Export cookies from your browser after
            logging in:
            <ul>
              <li>
                Chrome: Use extensions like "Cookie-Editor" or "EditThisCookie"
              </li>
              <li>Firefox: Use "Cookie Quick Manager" extension</li>
              <li>
                Manual: Copy cookies from Developer Tools → Application →
                Cookies
              </li>
            </ul>
          </li>
          <li>
            <strong>Method 3:</strong> Paste cookie data in Netscape format or
            JSON
          </li>
        </ol>
      </div>

      <div class="grid">
        <div class="section">
          <h2>Method 1: Capture from URL</h2>
          <div class="input-group">
            <input
              type="text"
              id="captureUrl"
              placeholder="https://example.com"
              value=""
            />
          </div>
          <div class="input-group">
            <textarea
              id="captureHeaders"
              placeholder='Optional headers (JSON format):
{
  "User-Agent": "Mozilla/5.0...",
  "Authorization": "Bearer token..."
}'
            ></textarea>
          </div>
          <div class="input-group">
            <textarea
              id="captureCookies"
              placeholder='Optional existing cookies (JSON format):
{
  "session_id": "abc123",
  "auth_token": "xyz789"
}'
            ></textarea>
          </div>
          <button onclick="captureFromUrl()">Capture Cookies</button>
        </div>

        <div class="section">
          <h2>Method 2: Import Browser Cookies</h2>
          <div class="input-group">
            <textarea
              id="cookiesText"
              placeholder="Paste cookies in Netscape format or one per line:
domain.com	TRUE	/	FALSE	1234567890	cookie_name	cookie_value"
            ></textarea>
          </div>
          <button onclick="importCookies()">Import Cookies</button>

          <div class="input-group" style="margin-top: 20px">
            <textarea
              id="cookiesJson"
              placeholder='Or paste cookies in JSON format:
[
  {
    "name": "session_id",
    "value": "abc123",
    "domain": ".example.com",
    "path": "/",
    "secure": true
  }
]'
            ></textarea>
          </div>
          <button onclick="importJsonCookies()">Import JSON Cookies</button>
        </div>
      </div>

      <div class="section">
        <h2>Captured/Imported Cookies</h2>
        <button class="success" onclick="analyzeCookies()">
          Analyze Cookies
        </button>
        <button onclick="showRawCookies()">Show Raw Data</button>

        <div class="tabs">
          <div class="tab active" onclick="switchTab('friendly')">
            User-Friendly View
          </div>
          <div class="tab" onclick="switchTab('table')">Table View</div>
          <div class="tab" onclick="switchTab('raw')">Raw JSON</div>
        </div>

        <div id="cookieResults">
          <div id="friendly-view" class="tab-content active"></div>
          <div id="table-view" class="tab-content"></div>
          <div id="raw-view" class="tab-content"></div>
        </div>
      </div>

      <div class="section">
        <h2>🔐 Session Data</h2>
        <button onclick="loadCurrentSession()">Load Current Session</button>
        <button onclick="analyzeSessionData()">Analyze Session</button>
        <div id="session-display"></div>
      </div>

      <div class="section">
        <h2>Cookie Analysis</h2>
        <div id="analysisResults"></div>
      </div>

      <div class="section">
        <h2>📊 Session Analysis</h2>
        <div id="session-analysis"></div>
      </div>
    </div>

    <script src="/static/cookie-display.js"></script>
    <script>
      async function captureFromUrl() {
        const url = document.getElementById("captureUrl").value;
        const headersText = document.getElementById("captureHeaders").value;
        const cookiesText = document.getElementById("captureCookies").value;

        if (!url) {
          alert("Please enter a URL");
          return;
        }

        try {
          const payload = { url: url };

          if (headersText.trim()) {
            payload.headers = JSON.parse(headersText);
          }

          if (cookiesText.trim()) {
            payload.cookies = JSON.parse(cookiesText);
          }

          const response = await fetch("/api/capture-cookies", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(payload),
          });

          const result = await response.json();

          if (result.status === "success") {
            updateAllViews(result);
          } else {
            alert("Error: " + result.message);
          }
        } catch (error) {
          alert("Error: " + error.message);
        }
      }

      async function importCookies() {
        const cookiesText = document.getElementById("cookiesText").value;

        if (!cookiesText.trim()) {
          alert("Please paste cookie data");
          return;
        }

        try {
          const response = await fetch("/api/import-cookies", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ cookies_text: cookiesText }),
          });

          const result = await response.json();

          if (result.status === "success") {
            updateAllViews({ cookies: result.cookies });
          } else {
            alert("Error: " + result.message);
          }
        } catch (error) {
          alert("Error: " + error.message);
        }
      }

      async function importJsonCookies() {
        const cookiesJson = document.getElementById("cookiesJson").value;

        if (!cookiesJson.trim()) {
          alert("Please paste JSON cookie data");
          return;
        }

        try {
          const cookies = JSON.parse(cookiesJson);

          const response = await fetch("/api/import-cookies", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ cookies_json: cookies }),
          });

          const result = await response.json();

          if (result.status === "success") {
            updateAllViews({ cookies: result.cookies });
          } else {
            alert("Error: " + result.message);
          }
        } catch (error) {
          alert("Error: " + error.message);
        }
      }

      async function analyzeCookies() {
        try {
          const response = await fetch("/api/analyze-cookies");
          const result = await response.json();

          if (result.status === "success") {
            displayEnhancedAnalysis(result.analysis);
          } else {
            alert("Error: " + result.message);
          }
        } catch (error) {
          alert("Error: " + error.message);
        }
      }

      async function showRawCookies() {
        try {
          const response = await fetch("/api/session-info");
          const data = await response.json();
          updateAllViews(data);
        } catch (error) {
          alert("Error: " + error.message);
        }
      }

      async function loadCurrentSession() {
        try {
          const response = await fetch("/api/session-info");
          const data = await response.json();

          if (data.session) {
            displaySessionData(data.session);
          } else {
            alert("No session data available");
          }
        } catch (error) {
          alert("Error loading session data: " + error.message);
        }
      }
    </script>
  </body>
</html>
