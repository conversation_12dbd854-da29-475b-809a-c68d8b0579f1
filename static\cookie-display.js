// Enhanced cookie display functions for user-friendly formatting

// Global variable to store current cookie data
let currentCookieData = null;

function switchTab(tabName) {
  // Remove active class from all tabs and content
  document
    .querySelectorAll(".tab")
    .forEach((tab) => tab.classList.remove("active"));
  document
    .querySelectorAll(".tab-content")
    .forEach((content) => content.classList.remove("active"));

  // Add active class to clicked tab and corresponding content
  event.target.classList.add("active");
  document.getElementById(tabName + "-view").classList.add("active");

  // If we have current cookie data, refresh the view
  if (currentCookieData) {
    if (tabName === "friendly") {
      displayFriendlyView(currentCookieData);
    } else if (tabName === "table") {
      displayTableView(currentCookieData);
    } else if (tabName === "raw") {
      displayRawView(currentCookieData);
    }
  }
}

function formatCookieValue(value, maxLength = 50) {
  if (!value) return "Not set";
  if (value.length > maxLength) {
    return value.substring(0, maxLength) + "...";
  }
  return value;
}

function formatExpiration(expires) {
  if (!expires || expires === "0" || expires === 0) {
    return "Session (expires when browser closes)";
  }

  try {
    const date = new Date(parseInt(expires) * 1000);
    return date.toLocaleString();
  } catch {
    return expires;
  }
}

function getSecurityBadges(cookie) {
  let badges = "";

  if (cookie.secure) {
    badges += '<span class="security-badge badge-secure">🔒 Secure</span>';
  } else {
    badges +=
      '<span class="security-badge badge-insecure">⚠️ Not Secure</span>';
  }

  if (cookie.httponly) {
    badges += '<span class="security-badge badge-httponly">🛡️ HttpOnly</span>';
  }

  if (!cookie.expires || cookie.expires === "0") {
    badges += '<span class="security-badge badge-session">⏱️ Session</span>';
  } else {
    badges +=
      '<span class="security-badge badge-persistent">💾 Persistent</span>';
  }

  return badges;
}

function getCookieTypeIcon(name) {
  const lowerName = name.toLowerCase();

  if (lowerName.includes("session") || lowerName.includes("sess")) {
    return "🔑"; // Session
  } else if (
    lowerName.includes("auth") ||
    lowerName.includes("token") ||
    lowerName.includes("login")
  ) {
    return "🛡️"; // Authentication
  } else if (
    lowerName.includes("track") ||
    lowerName.includes("analytics") ||
    lowerName.includes("ga")
  ) {
    return "📊"; // Tracking
  } else if (lowerName.includes("pref") || lowerName.includes("setting")) {
    return "⚙️"; // Preferences
  } else {
    return "🍪"; // Generic cookie
  }
}

function displayFriendlyView(data) {
  const friendlyDiv = document.getElementById("friendly-view");

  // Handle different data formats
  let cookies = {};
  if (data.cookies) {
    cookies = data.cookies;
  } else if (Array.isArray(data)) {
    // Convert array format to object format
    data.forEach((cookie) => {
      cookies[cookie.name] = cookie;
    });
  } else if (data.name && data.value) {
    // Single cookie format
    cookies[data.name] = data;
  }

  console.log("Displaying friendly view with cookies:", cookies);

  if (cookies && Object.keys(cookies).length > 0) {
    let html = "<h3>🍪 Cookie Summary</h3>";
    html += `<div class="alert alert-info">
            <strong>Total Cookies Found:</strong> ${
              Object.keys(cookies).length
            }<br>
            ${data.url ? `<strong>Source URL:</strong> ${data.url}<br>` : ""}
            ${
              data.status_code
                ? `<strong>HTTP Status:</strong> ${data.status_code}`
                : ""
            }
        </div>`;

    // Group cookies by domain
    const cookiesByDomain = {};
    Object.entries(cookies).forEach(([name, cookie]) => {
      const domain = cookie.domain || "Unknown Domain";
      if (!cookiesByDomain[domain]) {
        cookiesByDomain[domain] = [];
      }
      cookiesByDomain[domain].push({ name, ...cookie });
    });

    Object.entries(cookiesByDomain).forEach(([domain, cookies]) => {
      html += `
                <div class="cookie-category">
                    <div class="category-header">
                        🌐 ${domain} (${cookies.length} cookies)
                    </div>
                    <div class="category-content">
            `;

      cookies.forEach((cookie) => {
        const icon = getCookieTypeIcon(cookie.name);
        html += `
                    <div class="cookie-item">
                        <div class="cookie-name">${icon} ${cookie.name}</div>
                        <div class="cookie-details">
                            <div class="detail-item">
                                <span class="detail-label">Value:</span>
                                <span class="detail-value" title="${
                                  cookie.value
                                }">${formatCookieValue(cookie.value)}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Path:</span>
                                <span class="detail-value">${
                                  cookie.path || "/"
                                }</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Expires:</span>
                                <span class="detail-value">${formatExpiration(
                                  cookie.expires
                                )}</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Security:</span>
                                <span class="detail-value">${getSecurityBadges(
                                  cookie
                                )}</span>
                            </div>
                        </div>
                    </div>
                `;
      });

      html += "</div></div>";
    });
  } else {
    html =
      '<div class="alert alert-warning">No cookies found. Try capturing or importing cookies first.</div>';
  }

  friendlyDiv.innerHTML = html;
}

function displayTableView(data) {
  const tableDiv = document.getElementById("table-view");

  // Handle different data formats
  let cookies = {};
  if (data.cookies) {
    cookies = data.cookies;
  } else if (Array.isArray(data)) {
    // Convert array format to object format
    data.forEach((cookie) => {
      cookies[cookie.name] = cookie;
    });
  } else if (data.name && data.value) {
    // Single cookie format
    cookies[data.name] = data;
  }

  console.log("Displaying table view with cookies:", cookies);

  if (cookies && Object.keys(cookies).length > 0) {
    let html = "<h3>📊 Cookie Table</h3>";
    html += `
            <table class="cookie-table">
                <thead>
                    <tr>
                        <th>Type</th>
                        <th>Name</th>
                        <th>Value</th>
                        <th>Domain</th>
                        <th>Path</th>
                        <th>Expires</th>
                        <th>Security</th>
                    </tr>
                </thead>
                <tbody>
        `;

    Object.entries(cookies).forEach(([name, cookie]) => {
      const icon = getCookieTypeIcon(name);
      html += `
                <tr>
                    <td style="text-align: center; font-size: 1.2em;">${icon}</td>
                    <td><strong>${name}</strong></td>
                    <td class="cookie-value" title="${
                      cookie.value
                    }">${formatCookieValue(cookie.value, 30)}</td>
                    <td>${cookie.domain || "N/A"}</td>
                    <td>${cookie.path || "/"}</td>
                    <td>${formatExpiration(cookie.expires)}</td>
                    <td>${getSecurityBadges(cookie)}</td>
                </tr>
            `;
    });

    html += "</tbody></table>";
  } else {
    html =
      '<div class="alert alert-warning">No cookies to display in table format.</div>';
  }

  tableDiv.innerHTML = html;
}

function displayRawView(data) {
  const rawDiv = document.getElementById("raw-view");
  rawDiv.innerHTML = `
        <h3>🔧 Raw JSON Data</h3>
        <div class="alert alert-info">
            This is the raw JSON data returned by the server. Use this for debugging or integration with other tools.
        </div>
        <pre>${JSON.stringify(data, null, 2)}</pre>
    `;
}

function updateAllViews(data) {
  currentCookieData = data;
  displayFriendlyView(data);
  displayTableView(data);
  displayRawView(data);
}

// Enhanced analysis display
function displayEnhancedAnalysis(analysis) {
  const analysisDiv = document.getElementById("analysisResults");

  let html = `
        <h3>📈 Detailed Cookie Analysis</h3>
        
        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">${analysis.total_cookies}</div>
                <div>Total Cookies</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${
                  analysis.security_analysis.secure_cookies
                }</div>
                <div>🔒 Secure Cookies</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${
                  analysis.security_analysis.session_cookies
                }</div>
                <div>⏱️ Session Cookies</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">${
                  Object.keys(analysis.domains).length
                }</div>
                <div>🌐 Domains</div>
            </div>
        </div>
        
        <div class="grid">
            <div class="cookie-category">
                <div class="category-header">🔐 Security Analysis</div>
                <div class="category-content">
                    <div class="detail-item">
                        <span class="detail-label">Secure Cookies:</span>
                        <span class="detail-value">${
                          analysis.security_analysis.secure_cookies
                        } / ${analysis.total_cookies}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">HttpOnly Cookies:</span>
                        <span class="detail-value">${
                          analysis.security_analysis.httponly_cookies
                        } / ${analysis.total_cookies}</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Session Cookies:</span>
                        <span class="detail-value">${
                          analysis.security_analysis.session_cookies
                        }</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Persistent Cookies:</span>
                        <span class="detail-value">${
                          analysis.security_analysis.persistent_cookies
                        }</span>
                    </div>
                </div>
            </div>
            
            <div class="cookie-category">
                <div class="category-header">📊 Cookie Types</div>
                <div class="category-content">
                    <div class="detail-item">
                        <span class="detail-label">🛡️ Authentication:</span>
                        <span class="detail-value">${
                          analysis.cookie_types.authentication.length
                        } cookies</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">🔑 Session:</span>
                        <span class="detail-value">${
                          analysis.cookie_types.session.length
                        } cookies</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">📈 Tracking:</span>
                        <span class="detail-value">${
                          analysis.cookie_types.tracking.length
                        } cookies</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">⚙️ Preferences:</span>
                        <span class="detail-value">${
                          analysis.cookie_types.preferences.length
                        } cookies</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">🍪 Other:</span>
                        <span class="detail-value">${
                          analysis.cookie_types.other.length
                        } cookies</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="cookie-category">
            <div class="category-header">🌐 Domain Distribution</div>
            <div class="category-content">
                ${Object.entries(analysis.domains)
                  .map(
                    ([domain, count]) => `
                    <div class="detail-item">
                        <span class="detail-label">${domain}:</span>
                        <span class="detail-value">${count} cookies</span>
                    </div>
                `
                  )
                  .join("")}
            </div>
        </div>
        
        <details style="margin-top: 20px;">
            <summary style="cursor: pointer; font-weight: bold;">🔧 Full Analysis Data (JSON)</summary>
            <pre style="margin-top: 10px;">${JSON.stringify(
              analysis,
              null,
              2
            )}</pre>
        </details>
    `;

  analysisDiv.innerHTML = html;
}
