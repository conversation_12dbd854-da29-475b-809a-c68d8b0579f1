from flask import Flask, request, session, jsonify, render_template, make_response, redirect, url_for
import json
from datetime import datetime, timedelta
import secrets
import requests
from urllib.parse import urlparse, parse_qs
import base64

app = Flask(__name__)
app.secret_key = secrets.token_hex(16)  # Generate a secure secret key for sessions

@app.route('/')
def index():
    """Main page to display session and cookie information"""
    return render_template('index.html')

@app.route('/api/session-info')
def get_session_info():
    """API endpoint to get current session and cookie data"""

    # Get all cookies with detailed information
    cookies = {}
    for cookie_name, cookie_value in request.cookies.items():
        cookies[cookie_name] = {
            'value': cookie_value,
            'domain': request.host,
            'path': '/',
            'secure': request.is_secure,
            'httponly': False,  # Can't detect this from request
            'expires': None  # Session cookie by default
        }

    # Get session data with metadata
    session_data = dict(session)
    session_metadata = {
        'session_id': request.cookies.get('session', 'No session cookie'),
        'is_new': len(session_data) == 0,
        'size': len(str(session_data)),
        'keys_count': len(session_data.keys()),
        'last_accessed': datetime.now().isoformat()
    }

    # Get request headers (useful for debugging)
    headers = dict(request.headers)

    # Get client info
    client_info = {
        'ip_address': request.remote_addr,
        'user_agent': request.headers.get('User-Agent'),
        'method': request.method,
        'url': request.url,
        'timestamp': datetime.now().isoformat(),
        'referrer': request.headers.get('Referer', 'Direct access'),
        'accept_language': request.headers.get('Accept-Language', 'Not specified')
    }

    return jsonify({
        'cookies': cookies,
        'session': {
            'data': session_data,
            'metadata': session_metadata
        },
        'headers': headers,
        'client_info': client_info
    })

@app.route('/set-session', methods=['POST'])
def set_session_data():
    """Set session data"""
    data = request.get_json()
    
    if data:
        for key, value in data.items():
            session[key] = value
        return jsonify({'status': 'success', 'message': 'Session data set successfully'})
    
    return jsonify({'status': 'error', 'message': 'No data provided'}), 400

@app.route('/set-cookie', methods=['POST'])
def set_cookie():
    """Set a cookie"""
    data = request.get_json()
    
    if not data or 'name' not in data or 'value' not in data:
        return jsonify({'status': 'error', 'message': 'Cookie name and value required'}), 400
    
    response = make_response(jsonify({'status': 'success', 'message': 'Cookie set successfully'}))
    
    # Set cookie with optional parameters
    max_age = data.get('max_age', 3600)  # Default 1 hour
    secure = data.get('secure', False)
    httponly = data.get('httponly', False)
    samesite = data.get('samesite', 'Lax')
    
    response.set_cookie(
        data['name'], 
        data['value'],
        max_age=max_age,
        secure=secure,
        httponly=httponly,
        samesite=samesite
    )
    
    return response

@app.route('/clear-session', methods=['POST'])
def clear_session():
    """Clear all session data"""
    session.clear()
    return jsonify({'status': 'success', 'message': 'Session cleared successfully'})

@app.route('/clear-cookies', methods=['POST'])
def clear_cookies():
    """Clear all cookies"""
    response = make_response(jsonify({'status': 'success', 'message': 'Cookies cleared successfully'}))
    
    # Clear all cookies by setting them to expire
    for cookie_name in request.cookies:
        response.set_cookie(cookie_name, '', expires=0)
    
    return response

@app.route('/demo')
def demo():
    """Demo page to test session and cookie functionality"""
    # Set some demo session data with various types and categories
    if 'visit_count' not in session:
        session['visit_count'] = 0
    session['visit_count'] += 1
    session['last_visit'] = datetime.now().isoformat()
    session['user_id'] = session.get('user_id', f'user_{secrets.token_hex(4)}')
    session['username'] = session.get('username', 'demo_user')
    session['authenticated'] = True
    session['user_preferences'] = {
        'theme': 'dark',
        'language': 'en',
        'notifications': True
    }
    session['csrf_token'] = secrets.token_hex(16)
    session['shopping_cart'] = ['item1', 'item2']
    session['temp_data'] = 'This is temporary'
    session['created_at'] = session.get('created_at', datetime.now().isoformat())

    return render_template('demo.html')

@app.route('/capture')
def capture_page():
    """Page for capturing cookies from external websites"""
    return render_template('capture.html')

@app.route('/test-display')
def test_display():
    """Test page with sample cookie data pre-loaded"""
    # Load sample cookies into session for testing
    sample_cookies = [
        {
            "name": "session_id",
            "value": "abc123xyz789sessiontoken",
            "domain": ".example.com",
            "path": "/",
            "secure": True,
            "httponly": True,
            "expires": "0"
        },
        {
            "name": "auth_token",
            "value": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ",
            "domain": ".example.com",
            "path": "/",
            "secure": True,
            "httponly": True,
            "expires": "1735689600"
        },
        {
            "name": "user_preferences",
            "value": "theme=dark&lang=en&notifications=true",
            "domain": ".example.com",
            "path": "/",
            "secure": False,
            "httponly": False,
            "expires": "1767225600"
        },
        {
            "name": "_ga",
            "value": "GA1.2.123456789.1234567890",
            "domain": ".example.com",
            "path": "/",
            "secure": False,
            "httponly": False,
            "expires": "1767225600"
        },
        {
            "name": "_utm_source",
            "value": "google",
            "domain": ".example.com",
            "path": "/",
            "secure": False,
            "httponly": False,
            "expires": "1640995200"
        },
        {
            "name": "csrf_token",
            "value": "a1b2c3d4e5f6g7h8i9j0",
            "domain": ".example.com",
            "path": "/admin",
            "secure": True,
            "httponly": True,
            "expires": "0"
        }
    ]

    session['imported_cookies'] = sample_cookies
    session['import_timestamp'] = datetime.now().isoformat()

    return render_template('test_display.html')

@app.route('/api/capture-cookies', methods=['POST'])
def capture_cookies():
    """Capture cookies from an external website"""
    data = request.get_json()

    if not data or 'url' not in data:
        return jsonify({'status': 'error', 'message': 'URL is required'}), 400

    url = data['url']
    headers = data.get('headers', {})
    cookies = data.get('cookies', {})

    try:
        # Create a session to maintain cookies
        web_session = requests.Session()

        # Set any provided cookies
        for cookie_name, cookie_value in cookies.items():
            web_session.cookies.set(cookie_name, cookie_value)

        # Set any provided headers
        if headers:
            web_session.headers.update(headers)

        # Make the request
        response = web_session.get(url, timeout=10)

        # Extract cookies from response
        captured_cookies = {}
        for cookie in web_session.cookies:
            captured_cookies[cookie.name] = {
                'value': cookie.value,
                'domain': cookie.domain,
                'path': cookie.path,
                'secure': cookie.secure,
                'expires': cookie.expires,
                'httponly': hasattr(cookie, 'httponly') and cookie.httponly
            }

        # Extract response headers
        response_headers = dict(response.headers)

        # Parse any Set-Cookie headers for additional details
        set_cookie_headers = response.headers.get_list('Set-Cookie') if hasattr(response.headers, 'get_list') else []

        return jsonify({
            'status': 'success',
            'url': url,
            'status_code': response.status_code,
            'cookies': captured_cookies,
            'response_headers': response_headers,
            'set_cookie_headers': set_cookie_headers,
            'timestamp': datetime.now().isoformat()
        })

    except requests.exceptions.RequestException as e:
        return jsonify({'status': 'error', 'message': f'Request failed: {str(e)}'}), 500
    except Exception as e:
        return jsonify({'status': 'error', 'message': f'Unexpected error: {str(e)}'}), 500

@app.route('/api/import-cookies', methods=['POST'])
def import_cookies():
    """Import cookies from browser export or manual input"""
    data = request.get_json()

    if not data:
        return jsonify({'status': 'error', 'message': 'No data provided'}), 400

    try:
        imported_cookies = []

        if 'cookies_text' in data:
            # Parse cookies from text format (like browser export)
            cookies_text = data['cookies_text']
            lines = cookies_text.strip().split('\n')

            for line in lines:
                if line.strip() and not line.startswith('#'):
                    parts = line.split('\t')
                    if len(parts) >= 7:
                        cookie_info = {
                            'domain': parts[0],
                            'flag': parts[1],
                            'path': parts[2],
                            'secure': parts[3].lower() == 'true',
                            'expiration': parts[4],
                            'name': parts[5],
                            'value': parts[6]
                        }
                        imported_cookies.append(cookie_info)

        elif 'cookies_json' in data:
            # Parse cookies from JSON format
            imported_cookies = data['cookies_json']

        # Store imported cookies in session for analysis
        session['imported_cookies'] = imported_cookies
        session['import_timestamp'] = datetime.now().isoformat()

        return jsonify({
            'status': 'success',
            'message': f'Imported {len(imported_cookies)} cookies',
            'cookies': imported_cookies
        })

    except Exception as e:
        return jsonify({'status': 'error', 'message': f'Import failed: {str(e)}'}), 500

@app.route('/api/analyze-session')
def analyze_session():
    """Analyze current session data"""
    session_data = dict(session)

    analysis = {
        'session_info': {
            'total_keys': len(session_data.keys()),
            'total_size_bytes': len(str(session_data)),
            'is_empty': len(session_data) == 0,
            'has_user_data': any(key in session_data for key in ['user_id', 'username', 'user', 'login']),
            'has_auth_data': any(key in session_data for key in ['auth', 'token', 'authenticated', 'logged_in']),
            'session_age': session.get('created_at', 'Unknown')
        },
        'data_types': {
            'strings': 0,
            'numbers': 0,
            'booleans': 0,
            'lists': 0,
            'dicts': 0,
            'other': 0
        },
        'key_categories': {
            'user_related': [],
            'authentication': [],
            'preferences': [],
            'temporary': [],
            'system': [],
            'other': []
        },
        'security_analysis': {
            'contains_sensitive_data': False,
            'potential_pii': [],
            'session_tokens': [],
            'csrf_tokens': []
        }
    }

    # Analyze each session key-value pair
    for key, value in session_data.items():
        # Data type analysis
        if isinstance(value, str):
            analysis['data_types']['strings'] += 1
        elif isinstance(value, (int, float)):
            analysis['data_types']['numbers'] += 1
        elif isinstance(value, bool):
            analysis['data_types']['booleans'] += 1
        elif isinstance(value, list):
            analysis['data_types']['lists'] += 1
        elif isinstance(value, dict):
            analysis['data_types']['dicts'] += 1
        else:
            analysis['data_types']['other'] += 1

        # Key categorization
        key_lower = key.lower()
        if any(keyword in key_lower for keyword in ['user', 'username', 'name', 'profile']):
            analysis['key_categories']['user_related'].append(key)
        elif any(keyword in key_lower for keyword in ['auth', 'token', 'login', 'password']):
            analysis['key_categories']['authentication'].append(key)
        elif any(keyword in key_lower for keyword in ['pref', 'setting', 'config', 'theme']):
            analysis['key_categories']['preferences'].append(key)
        elif any(keyword in key_lower for keyword in ['temp', 'cache', 'tmp', 'flash']):
            analysis['key_categories']['temporary'].append(key)
        elif any(keyword in key_lower for keyword in ['_', 'csrf', 'session', 'id']):
            analysis['key_categories']['system'].append(key)
        else:
            analysis['key_categories']['other'].append(key)

        # Security analysis
        if any(keyword in key_lower for keyword in ['password', 'secret', 'key', 'token']):
            analysis['security_analysis']['contains_sensitive_data'] = True

        if any(keyword in key_lower for keyword in ['email', 'phone', 'address', 'ssn']):
            analysis['security_analysis']['potential_pii'].append(key)

        if 'token' in key_lower:
            analysis['security_analysis']['session_tokens'].append(key)

        if 'csrf' in key_lower:
            analysis['security_analysis']['csrf_tokens'].append(key)

    return jsonify({
        'status': 'success',
        'session_data': session_data,
        'analysis': analysis,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/analyze-cookies')
def analyze_cookies():
    """Analyze imported or captured cookies"""
    imported_cookies = session.get('imported_cookies', [])

    if not imported_cookies:
        return jsonify({'status': 'error', 'message': 'No cookies to analyze'}), 400

    analysis = {
        'total_cookies': len(imported_cookies),
        'domains': {},
        'security_analysis': {
            'secure_cookies': 0,
            'httponly_cookies': 0,
            'session_cookies': 0,
            'persistent_cookies': 0
        },
        'cookie_types': {
            'authentication': [],
            'session': [],
            'tracking': [],
            'preferences': [],
            'other': []
        }
    }

    for cookie in imported_cookies:
        # Domain analysis
        domain = cookie.get('domain', 'unknown')
        if domain not in analysis['domains']:
            analysis['domains'][domain] = 0
        analysis['domains'][domain] += 1

        # Security analysis
        if cookie.get('secure', False):
            analysis['security_analysis']['secure_cookies'] += 1
        if cookie.get('httponly', False):
            analysis['security_analysis']['httponly_cookies'] += 1

        # Session vs persistent
        if cookie.get('expiration') == '0' or not cookie.get('expiration'):
            analysis['security_analysis']['session_cookies'] += 1
        else:
            analysis['security_analysis']['persistent_cookies'] += 1

        # Cookie type classification (basic heuristics)
        name = cookie.get('name', '').lower()
        if any(keyword in name for keyword in ['session', 'sess', 'jsession', 'phpsess']):
            analysis['cookie_types']['session'].append(cookie)
        elif any(keyword in name for keyword in ['auth', 'login', 'token', 'jwt']):
            analysis['cookie_types']['authentication'].append(cookie)
        elif any(keyword in name for keyword in ['track', 'analytics', 'ga', '_utm']):
            analysis['cookie_types']['tracking'].append(cookie)
        elif any(keyword in name for keyword in ['pref', 'setting', 'config']):
            analysis['cookie_types']['preferences'].append(cookie)
        else:
            analysis['cookie_types']['other'].append(cookie)

    return jsonify({
        'status': 'success',
        'analysis': analysis,
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
