from flask import Flask, request, session, jsonify, render_template, make_response, redirect, url_for
import json
from datetime import datetime, timedelta
import secrets

app = Flask(__name__)
app.secret_key = secrets.token_hex(16)  # Generate a secure secret key for sessions

@app.route('/')
def index():
    """Main page to display session and cookie information"""
    return render_template('index.html')

@app.route('/api/session-info')
def get_session_info():
    """API endpoint to get current session and cookie data"""
    
    # Get all cookies
    cookies = {}
    for cookie_name, cookie_value in request.cookies.items():
        cookies[cookie_name] = cookie_value
    
    # Get session data
    session_data = dict(session)
    
    # Get request headers (useful for debugging)
    headers = dict(request.headers)
    
    # Get client info
    client_info = {
        'ip_address': request.remote_addr,
        'user_agent': request.headers.get('User-Agent'),
        'method': request.method,
        'url': request.url,
        'timestamp': datetime.now().isoformat()
    }
    
    return jsonify({
        'cookies': cookies,
        'session': session_data,
        'headers': headers,
        'client_info': client_info
    })

@app.route('/set-session', methods=['POST'])
def set_session_data():
    """Set session data"""
    data = request.get_json()
    
    if data:
        for key, value in data.items():
            session[key] = value
        return jsonify({'status': 'success', 'message': 'Session data set successfully'})
    
    return jsonify({'status': 'error', 'message': 'No data provided'}), 400

@app.route('/set-cookie', methods=['POST'])
def set_cookie():
    """Set a cookie"""
    data = request.get_json()
    
    if not data or 'name' not in data or 'value' not in data:
        return jsonify({'status': 'error', 'message': 'Cookie name and value required'}), 400
    
    response = make_response(jsonify({'status': 'success', 'message': 'Cookie set successfully'}))
    
    # Set cookie with optional parameters
    max_age = data.get('max_age', 3600)  # Default 1 hour
    secure = data.get('secure', False)
    httponly = data.get('httponly', False)
    samesite = data.get('samesite', 'Lax')
    
    response.set_cookie(
        data['name'], 
        data['value'],
        max_age=max_age,
        secure=secure,
        httponly=httponly,
        samesite=samesite
    )
    
    return response

@app.route('/clear-session', methods=['POST'])
def clear_session():
    """Clear all session data"""
    session.clear()
    return jsonify({'status': 'success', 'message': 'Session cleared successfully'})

@app.route('/clear-cookies', methods=['POST'])
def clear_cookies():
    """Clear all cookies"""
    response = make_response(jsonify({'status': 'success', 'message': 'Cookies cleared successfully'}))
    
    # Clear all cookies by setting them to expire
    for cookie_name in request.cookies:
        response.set_cookie(cookie_name, '', expires=0)
    
    return response

@app.route('/demo')
def demo():
    """Demo page to test session and cookie functionality"""
    # Set some demo session data
    if 'visit_count' not in session:
        session['visit_count'] = 0
    session['visit_count'] += 1
    session['last_visit'] = datetime.now().isoformat()
    session['user_id'] = session.get('user_id', f'user_{secrets.token_hex(4)}')
    
    return render_template('demo.html')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
