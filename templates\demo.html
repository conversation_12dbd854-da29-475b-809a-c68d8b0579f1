<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Page - Session & Cookies</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .nav-links {
            text-align: center;
            margin: 20px 0;
        }
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            padding: 10px 15px;
            border: 1px solid #007bff;
            border-radius: 5px;
        }
        .nav-links a:hover {
            background: #007bff;
            color: white;
        }
        button {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #218838;
        }
        pre {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e0e0e0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Demo Page</h1>
        
        <div class="nav-links">
            <a href="/">Home</a>
            <a href="/demo">Demo Page</a>
            <a href="/api/session-info" target="_blank">Raw JSON Data</a>
        </div>

        <div class="info-box">
            <h3>Welcome to the Demo Page!</h3>
            <p>This page automatically sets some demo session data when you visit it. Each time you refresh or visit this page, the visit count will increase.</p>
        </div>

        <div class="info-box">
            <h3>What happens when you visit this page:</h3>
            <ul>
                <li>Visit count is tracked in the session</li>
                <li>Last visit timestamp is recorded</li>
                <li>A unique user ID is generated (if not already present)</li>
            </ul>
        </div>

        <button onclick="refreshData()">Show Current Session Data</button>
        <button onclick="window.location.reload()">Refresh Page (Increase Visit Count)</button>
        
        <div id="sessionDisplay" style="margin-top: 20px;"></div>
    </div>

    <script>
        async function refreshData() {
            try {
                const response = await fetch('/api/session-info');
                const data = await response.json();
                
                const displayDiv = document.getElementById('sessionDisplay');
                displayDiv.innerHTML = `
                    <h3>Current Session Data:</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('sessionDisplay').innerHTML = 
                    '<p style="color: red;">Error loading data: ' + error.message + '</p>';
            }
        }

        // Automatically show session data when page loads
        window.onload = refreshData;
    </script>
</body>
</html>
