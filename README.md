# Session Data & Cookies Web Application

A Python Flask web application for viewing, managing, and testing session data and cookies.

## Features

- **View Session Data**: Display current session variables and their values
- **View Cookies**: Show all cookies sent by the browser
- **Set Session Data**: Add or update session variables via web interface
- **Set Cookies**: Create new cookies with customizable parameters
- **Clear Data**: Remove all session data or cookies
- **Demo Page**: Test page that automatically creates sample session data
- **API Endpoints**: JSON API for programmatic access

## Installation

1. Install Python dependencies:
```bash
pip install -r requirements.txt
```

2. Run the application:
```bash
python app.py
```

3. Open your browser and navigate to:
```
http://localhost:5000
```

## Usage

### Web Interface

- **Home Page** (`/`): Main interface for viewing and managing session data and cookies
- **Demo Page** (`/demo`): Test page that creates sample session data
- **API Endpoint** (`/api/session-info`): JSON endpoint returning all session and cookie data

### API Endpoints

#### GET `/api/session-info`
Returns JSON with current session data, cookies, headers, and client information.

#### POST `/set-session`
Set session data by sending J<PERSON><PERSON>:
```json
{
  "username": "john",
  "role": "admin",
  "preferences": {"theme": "dark"}
}
```

#### POST `/set-cookie`
Set a cookie by sending JSON:
```json
{
  "name": "user_preference",
  "value": "dark_mode",
  "max_age": 3600,
  "secure": false,
  "httponly": false,
  "samesite": "Lax"
}
```

#### POST `/clear-session`
Clear all session data.

#### POST `/clear-cookies`
Clear all cookies.

## File Structure

```
SessionData/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── README.md             # This file
└── templates/
    ├── index.html        # Main web interface
    └── demo.html         # Demo page
```

## Security Notes

- The application generates a secure random secret key for session encryption
- Cookies can be configured with security flags (secure, httponly, samesite)
- The application runs in debug mode by default - disable for production use

## Development

To modify the application:

1. Edit `app.py` for backend functionality
2. Edit templates in `templates/` for frontend changes
3. The application will auto-reload when files are changed (debug mode)

## Testing

Visit the demo page to see automatic session tracking in action, or use the main interface to manually test session and cookie functionality.
