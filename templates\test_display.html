<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Test User-Friendly <PERSON><PERSON>lay</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #333;
        text-align: center;
        margin-bottom: 30px;
      }
      .nav-links {
        text-align: center;
        margin: 20px 0;
      }
      .nav-links a {
        color: #007bff;
        text-decoration: none;
        margin: 0 15px;
        padding: 10px 15px;
        border: 1px solid #007bff;
        border-radius: 5px;
      }
      .nav-links a:hover {
        background: #007bff;
        color: white;
      }
      .alert {
        padding: 15px;
        margin: 10px 0;
        border-radius: 5px;
      }
      .alert-info {
        background: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
      }
      .tabs {
        display: flex;
        border-bottom: 2px solid #dee2e6;
        margin: 20px 0;
      }
      .tab {
        padding: 12px 24px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-bottom: none;
        cursor: pointer;
        margin-right: 5px;
        border-radius: 8px 8px 0 0;
      }
      .tab.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
      }
      .tab-content {
        display: none;
      }
      .tab-content.active {
        display: block;
      }
      button {
        background: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin: 5px;
      }
      button:hover {
        background: #0056b3;
      }
      button.success {
        background: #28a745;
      }
      button.success:hover {
        background: #218838;
      }
      /* Include all the cookie display styles from capture.html */
      .cookie-table {
        width: 100%;
        border-collapse: collapse;
        margin: 20px 0;
      }
      .cookie-table th,
      .cookie-table td {
        border: 1px solid #ddd;
        padding: 12px;
        text-align: left;
      }
      .cookie-table th {
        background-color: #f8f9fa;
        font-weight: bold;
        color: #495057;
      }
      .cookie-table tr:nth-child(even) {
        background-color: #f8f9fa;
      }
      .cookie-table tr:hover {
        background-color: #e9ecef;
      }
      .cookie-value {
        max-width: 200px;
        word-break: break-all;
        font-family: monospace;
        font-size: 0.9em;
      }
      .security-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: bold;
        margin: 2px;
      }
      .badge-secure {
        background-color: #d4edda;
        color: #155724;
      }
      .badge-insecure {
        background-color: #f8d7da;
        color: #721c24;
      }
      .badge-httponly {
        background-color: #d1ecf1;
        color: #0c5460;
      }
      .badge-session {
        background-color: #fff3cd;
        color: #856404;
      }
      .badge-persistent {
        background-color: #e2e3e5;
        color: #383d41;
      }
      .cookie-category {
        margin: 20px 0;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
      }
      .category-header {
        background-color: #007bff;
        color: white;
        padding: 15px;
        font-weight: bold;
        font-size: 1.1em;
      }
      .category-content {
        padding: 15px;
      }
      .cookie-item {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .cookie-name {
        font-weight: bold;
        font-size: 1.1em;
        color: #007bff;
        margin-bottom: 8px;
      }
      .cookie-details {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin: 10px 0;
      }
      .detail-item {
        display: flex;
        justify-content: space-between;
        padding: 5px 0;
        border-bottom: 1px solid #f1f3f4;
      }
      .detail-label {
        font-weight: bold;
        color: #6c757d;
      }
      .detail-value {
        color: #495057;
        font-family: monospace;
        word-break: break-all;
      }
      .stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;
      }
      .stat-card {
        background: #e7f3ff;
        padding: 15px;
        border-radius: 5px;
        text-align: center;
      }
      .stat-number {
        font-size: 2em;
        font-weight: bold;
        color: #007bff;
      }
      pre {
        background: #f8f8f8;
        padding: 15px;
        border-radius: 5px;
        overflow-x: auto;
        border: 1px solid #e0e0e0;
        max-height: 400px;
        overflow-y: auto;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>🍪 User-Friendly Cookie Display Test</h1>

      <div class="nav-links">
        <a href="/">Home</a>
        <a href="/demo">Demo Page</a>
        <a href="/capture">Cookie Capture</a>
        <a href="/test-display">Test Display</a>
      </div>

      <div class="alert alert-info">
        <h3>✅ Sample Cookie Data Loaded!</h3>
        <p>
          This page demonstrates the user-friendly cookie display with
          pre-loaded sample data. The sample includes various types of cookies:
          session, authentication, preferences, tracking, and CSRF tokens.
        </p>
      </div>

      <div style="text-align: center; margin: 20px 0">
        <button class="success" onclick="loadSampleData()">
          Load Sample Cookies
        </button>
        <button onclick="analyzeSampleData()">Analyze Cookie Data</button>
        <button onclick="loadSessionData()">Load Session Data</button>
        <button onclick="analyzeSessionData()">Analyze Session Data</button>
      </div>

      <div class="tabs">
        <div class="tab active" onclick="switchTab('friendly')">
          🍪 User-Friendly View
        </div>
        <div class="tab" onclick="switchTab('table')">📊 Table View</div>
        <div class="tab" onclick="switchTab('raw')">🔧 Raw JSON</div>
      </div>

      <div id="cookieResults">
        <div id="friendly-view" class="tab-content active"></div>
        <div id="table-view" class="tab-content"></div>
        <div id="raw-view" class="tab-content"></div>
      </div>

      <div style="margin-top: 30px">
        <h2>🔐 Session Data</h2>
        <div id="session-display"></div>
      </div>

      <div style="margin-top: 30px">
        <h2>📈 Cookie Analysis</h2>
        <div id="analysisResults"></div>
      </div>

      <div style="margin-top: 30px">
        <h2>📊 Session Analysis</h2>
        <div id="session-analysis"></div>
      </div>
    </div>

    <script src="/static/cookie-display.js"></script>
    <script>
      // Sample cookie data
      const sampleCookieData = {
        cookies: {
          session_id: {
            value: "abc123xyz789sessiontoken",
            domain: ".example.com",
            path: "/",
            secure: true,
            httponly: true,
            expires: "0",
          },
          auth_token: {
            value:
              "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ",
            domain: ".example.com",
            path: "/",
            secure: true,
            httponly: true,
            expires: "1735689600",
          },
          user_preferences: {
            value: "theme=dark&lang=en&notifications=true",
            domain: ".example.com",
            path: "/",
            secure: false,
            httponly: false,
            expires: "1767225600",
          },
          _ga: {
            value: "GA1.2.123456789.1234567890",
            domain: ".example.com",
            path: "/",
            secure: false,
            httponly: false,
            expires: "1767225600",
          },
          _utm_source: {
            value: "google",
            domain: ".example.com",
            path: "/",
            secure: false,
            httponly: false,
            expires: "1640995200",
          },
          csrf_token: {
            value: "a1b2c3d4e5f6g7h8i9j0",
            domain: ".example.com",
            path: "/admin",
            secure: true,
            httponly: true,
            expires: "0",
          },
        },
        url: "https://example.com",
        status_code: 200,
      };

      function loadSampleData() {
        updateAllViews(sampleCookieData);
      }

      async function analyzeSampleData() {
        try {
          const response = await fetch("/api/analyze-cookies");
          const result = await response.json();

          if (result.status === "success") {
            displayEnhancedAnalysis(result.analysis);
          } else {
            alert("Error: " + result.message);
          }
        } catch (error) {
          alert("Error: " + error.message);
        }
      }

      async function loadSessionData() {
        try {
          const response = await fetch("/api/session-info");
          const data = await response.json();

          if (data.session) {
            displaySessionData(data.session);
          } else {
            alert("No session data available");
          }
        } catch (error) {
          alert("Error loading session data: " + error.message);
        }
      }

      // Load sample data when page loads
      window.onload = function () {
        loadSampleData();
        loadSessionData();
      };
    </script>
  </body>
</html>
