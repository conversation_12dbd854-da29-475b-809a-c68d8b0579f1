# Cookie Capture Guide - Extract Session Data from Real Websites

This guide explains how to capture and analyze cookies and session data from websites after logging in with your credentials.

## 🎯 Overview

The enhanced web application now provides three methods to capture cookies from real websites:

1. **URL Capture** - Fetch cookies from public pages
2. **Browser Export** - Export cookies after logging in
3. **Manual Copy** - Copy cookies from browser developer tools

## 📋 Method 1: Browser Export (Recommended for Login Sessions)

### Chrome Browser:
1. **Install Cookie Extension:**
   - Install "Cookie-Editor" or "EditThisCookie" from Chrome Web Store
   - Or use "Export Cookies" extension

2. **Login to Target Website:**
   - Navigate to the website
   - Login with your credentials
   - Complete any 2FA if required

3. **Export Cookies:**
   - Click the cookie extension icon
   - Select "Export" or "Export All"
   - Choose format: Netscape or JSON
   - Copy the exported data

4. **Import to Application:**
   - Go to `/capture` page in our application
   - Paste the exported data in the import section
   - Click "Import Cookies"

### Firefox Browser:
1. **Install Extension:**
   - Install "Cookie Quick Manager" extension

2. **Export Process:**
   - Login to target website
   - Open Cookie Quick Manager
   - Select all cookies for the domain
   - Export as Netscape format

## 📋 Method 2: Developer Tools (Manual)

### Chrome/Edge Developer Tools:
1. **Login to Website:**
   - Navigate and login to target website

2. **Open Developer Tools:**
   - Press F12 or Ctrl+Shift+I
   - Go to "Application" tab
   - Click "Cookies" in left sidebar
   - Select the website domain

3. **Copy Cookie Data:**
   - Right-click on cookie list
   - Select "Copy" or manually copy each cookie
   - Format: `name=value; domain=.example.com; path=/`

4. **Import to Application:**
   - Use the manual import section
   - Paste cookie data in JSON format

### Firefox Developer Tools:
1. **Open Tools:**
   - Press F12
   - Go to "Storage" tab
   - Click "Cookies"

2. **Export Cookies:**
   - Select domain
   - Copy cookie values manually

## 📋 Method 3: URL Capture (Limited)

This method works for public pages but won't capture login session cookies:

1. **Enter URL:**
   - Go to `/capture` page
   - Enter the website URL
   - Optionally add headers (User-Agent, etc.)

2. **Capture:**
   - Click "Capture Cookies"
   - View results and analysis

## 🔍 Cookie Analysis Features

Once cookies are imported, the application provides:

### Security Analysis:
- **Secure Cookies:** Count of cookies with secure flag
- **HttpOnly Cookies:** Cookies protected from JavaScript
- **Session vs Persistent:** Temporary vs stored cookies
- **Domain Analysis:** Cookies grouped by domain

### Cookie Classification:
- **Authentication:** Login tokens, session IDs
- **Session:** Session management cookies
- **Tracking:** Analytics and tracking cookies
- **Preferences:** User settings and preferences

### Detailed Information:
- Cookie names and values
- Expiration dates
- Domain and path restrictions
- Security flags

## 🛡️ Security Considerations

### Important Warnings:
- **Never share session cookies** - They contain your login credentials
- **Use in secure environment** - Only run on trusted networks
- **Clear data after analysis** - Don't store sensitive cookies long-term
- **Be aware of expiration** - Session cookies expire quickly

### Best Practices:
1. **Test Environment:** Use test accounts when possible
2. **Limited Scope:** Only capture cookies you need to analyze
3. **Immediate Analysis:** Analyze cookies immediately after capture
4. **Clean Up:** Clear imported data after analysis

## 📊 Example Use Cases

### 1. Session Management Analysis:
- Understand how a website manages user sessions
- Identify session timeout behavior
- Analyze session security implementation

### 2. Authentication Flow Study:
- Examine login token structure
- Understand multi-factor authentication cookies
- Analyze remember-me functionality

### 3. Tracking and Privacy:
- Identify tracking cookies
- Understand data collection practices
- Analyze third-party integrations

### 4. Security Assessment:
- Check for secure cookie implementation
- Identify potential security vulnerabilities
- Analyze cookie-based attack vectors

## 🔧 Troubleshooting

### Common Issues:

1. **No Cookies Captured:**
   - Ensure you're logged in to the website
   - Check if cookies are set for the correct domain
   - Verify the website uses cookies (not just localStorage)

2. **Import Errors:**
   - Check cookie format (Netscape vs JSON)
   - Ensure proper escaping of special characters
   - Verify complete cookie data

3. **Analysis Shows No Data:**
   - Confirm cookies were imported successfully
   - Check if cookies have expired
   - Verify the analysis endpoint is working

## 📝 Cookie Format Examples

### Netscape Format:
```
.example.com	TRUE	/	FALSE	1640995200	session_id	abc123xyz
.example.com	TRUE	/	TRUE	1640995200	auth_token	def456uvw
```

### JSON Format:
```json
[
  {
    "name": "session_id",
    "value": "abc123xyz",
    "domain": ".example.com",
    "path": "/",
    "secure": false,
    "httponly": true,
    "expires": "2024-12-31T23:59:59Z"
  }
]
```

## 🚀 Advanced Features

### Custom Headers:
Add custom headers when capturing from URLs:
```json
{
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
  "Authorization": "Bearer your-token-here",
  "X-Custom-Header": "custom-value"
}
```

### Existing Cookies:
Send existing cookies with URL capture:
```json
{
  "session_id": "existing-session",
  "csrf_token": "csrf-value"
}
```

This comprehensive approach allows you to capture and analyze session data from any website after logging in with your credentials!
