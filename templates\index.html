<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Session Data & Cookies Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .section h2 {
            margin-top: 0;
            color: #555;
        }
        pre {
            background: #f8f8f8;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            border: 1px solid #e0e0e0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.danger {
            background: #dc3545;
        }
        button.danger:hover {
            background: #c82333;
        }
        .input-group {
            margin: 10px 0;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .controls {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .nav-links {
            text-align: center;
            margin: 20px 0;
        }
        .nav-links a {
            color: #007bff;
            text-decoration: none;
            margin: 0 15px;
            padding: 10px 15px;
            border: 1px solid #007bff;
            border-radius: 5px;
        }
        .nav-links a:hover {
            background: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Session Data & Cookies Viewer</h1>
        
        <div class="nav-links">
            <a href="/">Home</a>
            <a href="/demo">Demo Page</a>
            <a href="/api/session-info" target="_blank">Raw JSON Data</a>
        </div>

        <div class="section">
            <h2>Current Session & Cookie Data</h2>
            <button onclick="refreshData()">Refresh Data</button>
            <pre id="sessionData">Loading...</pre>
        </div>

        <div class="controls">
            <div class="section">
                <h2>Set Session Data</h2>
                <div class="input-group">
                    <textarea id="sessionInput" placeholder='{"key": "value", "user": "john"}'></textarea>
                </div>
                <button onclick="setSession()">Set Session Data</button>
                <button class="danger" onclick="clearSession()">Clear Session</button>
            </div>

            <div class="section">
                <h2>Set Cookie</h2>
                <div class="input-group">
                    <input type="text" id="cookieName" placeholder="Cookie Name">
                    <input type="text" id="cookieValue" placeholder="Cookie Value">
                    <input type="number" id="cookieMaxAge" placeholder="Max Age (seconds)" value="3600">
                </div>
                <button onclick="setCookie()">Set Cookie</button>
                <button class="danger" onclick="clearCookies()">Clear All Cookies</button>
            </div>
        </div>
    </div>

    <script>
        async function refreshData() {
            try {
                const response = await fetch('/api/session-info');
                const data = await response.json();
                document.getElementById('sessionData').textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                document.getElementById('sessionData').textContent = 'Error loading data: ' + error.message;
            }
        }

        async function setSession() {
            const input = document.getElementById('sessionInput').value;
            try {
                const data = JSON.parse(input);
                const response = await fetch('/set-session', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify(data)
                });
                const result = await response.json();
                alert(result.message);
                refreshData();
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        async function setCookie() {
            const name = document.getElementById('cookieName').value;
            const value = document.getElementById('cookieValue').value;
            const maxAge = document.getElementById('cookieMaxAge').value;
            
            if (!name || !value) {
                alert('Please provide both cookie name and value');
                return;
            }

            try {
                const response = await fetch('/set-cookie', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({
                        name: name,
                        value: value,
                        max_age: parseInt(maxAge)
                    })
                });
                const result = await response.json();
                alert(result.message);
                refreshData();
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        async function clearSession() {
            try {
                const response = await fetch('/clear-session', {method: 'POST'});
                const result = await response.json();
                alert(result.message);
                refreshData();
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        async function clearCookies() {
            try {
                const response = await fetch('/clear-cookies', {method: 'POST'});
                const result = await response.json();
                alert(result.message);
                refreshData();
            } catch (error) {
                alert('Error: ' + error.message);
            }
        }

        // Load data when page loads
        window.onload = refreshData;
    </script>
</body>
</html>
